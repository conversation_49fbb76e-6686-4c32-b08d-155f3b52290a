# coding=utf-8
"""
Specialized field mapping and processing utilities for JIRA data types.

This module provides specialized processors for changelog, worklog, comment,
and issue links data with their specific DataFrame transformations.
"""
import json
import sys
import traceback
from datetime import datetime
from logging import Logger

import yaml
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from dags.data_pipeline.dbmodels.changelog import ChangelogJSON
from dags.data_pipeline.dbmodels.initiativeattribute import InitiativeAttribute
from dags.data_pipeline.dbmodels.issue import Issue, IssueComments, IssueLinks
from dags.data_pipeline.dbmodels.worklog import WorkLog
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_dataframe_minimal
from dags.data_pipeline.database.get_model_by_name import get_model_by_name

@dataclass
class SpecializedFieldConfig:
    """Configuration for specialized field processing."""
    fields: List[Dict[str, Any]]
    processing_steps: List[str]
    database_config: Dict[str, Any]
    drop_column_prefixes: List[str]
    drop_column_exceptions: List[str]

def add_issue_key_issue_id(d, issue_key, issue_id):
    try:
        d['issue_key'] = issue_key
        d['issue_id'] = issue_id
        return d
    except Exception as e:
        raise e

class SpecializedFieldMapper:
    """Handles specialized field mapping for different JIRA data types."""
    
    def __init__(self, data_type: str):
        """
        Initialize the specialized field mapper.
        
        Args:
            data_type: Type of data (changelog, worklog, comment, issue_links)
        """
        self.data_type = data_type
        self.config_path = Path(__file__).parent / f"{data_type}_fields.yaml"
        self.config: Optional[SpecializedFieldConfig] = None
        self._load_configuration()
    
    def _load_configuration(self):
        """Load specialized field configuration from YAML."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config_data = yaml.safe_load(file)
            
            self.config = SpecializedFieldConfig(
                fields=config_data.get('fields', []),
                processing_steps=config_data.get('processing_steps', []),
                database_config=config_data.get('database_config', {}),
                drop_column_prefixes=config_data.get('drop-column-prefixes', []),
                drop_column_exceptions=config_data.get('drop-column-exceptions', [])
            )
                        
        except Exception as e:
            raise RuntimeError(f"Failed to load specialized field configuration from {self.config_path}: {e}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration for this data type."""
        if not self.config:
            return {}

        db_config = dict(self.config.database_config)  # make a copy

        model_value = db_config.get("model")
        if isinstance(model_value, str):
            try:
                # Use get_model_by_name instead of hardcoded MODEL_REGISTRY
                db_config["model"] = get_model_by_name(model_value)
            except ValueError as e:
                raise ValueError(f"Model '{model_value}' not found: {e}")

        return db_config
    
    def get_processing_steps(self) -> List[str]:
        """Get processing steps for this data type."""
        return self.config.processing_steps if self.config else []
    
    def get_field_mappings(self) -> Dict[str, str]:
        """Get field mappings for column renaming."""
        mappings = {}
        if self.config:
            for field in self.config.fields:
                source = field.get('source_column')
                target = field.get('target_column')
                if source and target and source != target:
                    mappings[source] = target
        return mappings
    
    def get_type_mappings(self) -> Dict[str, str]:
        """Get type mappings for data conversion."""
        mappings = {}
        if self.config:
            for field in self.config.fields:
                target_col = field.get('target_column')
                target_type = field.get('target_type')
                if target_col and target_type:
                    mappings[target_col] = target_type
        return mappings
    
    def get_columns_to_drop(self) -> List[str]:
        """Get columns that should be dropped after processing."""
        columns = []
        if self.config:
            for field in self.config.fields:
                if field.get('drop_after_processing', False):
                    columns.append(field.get('target_column', field.get('source_column')))
        return columns

    def get_drop_column_prefixes(self) -> List[str]:
        """Get list of column prefixes that should be dropped."""
        return self.config.drop_column_prefixes if self.config else []

    def get_drop_column_exceptions(self) -> List[str]:
        """Get list of columns that should NOT be dropped despite matching prefixes."""
        return self.config.drop_column_exceptions if self.config else []

    def drop_unwanted_columns(self, df: pd.DataFrame, my_logger=None) -> pd.DataFrame:
        """Drop unwanted columns based on configuration."""
        drop_prefixes = self.get_drop_column_prefixes()
        exceptions = set(self.get_drop_column_exceptions())

        try:
            columns_to_drop = [
                col for col in df.columns
                if any(col.startswith(prefix) for prefix in drop_prefixes) and col not in exceptions
            ]
        except AttributeError as e:
            my_logger.error(f"drop_prefixes = {drop_prefixes}")
            my_logger.error(f"exceptions = {exceptions}")
            my_logger.error(f"Failed to drop columns: {[col for col in df.columns]}")
            my_logger.error(f"Failed to drop columns: {e}")
            raise e

        if columns_to_drop:
            df.drop(columns=columns_to_drop, inplace=True)
            if my_logger:
                my_logger.debug(f"Dropped {len(columns_to_drop)} unwanted columns: {columns_to_drop}")

        return df

    def apply_field_mappings(self, df: pd.DataFrame, my_logger=None) -> pd.DataFrame:
        """
        Apply field mappings to a DataFrame based on YAML configuration.

        Args:
            df: Input DataFrame
            my_logger: Logger instance

        Returns:
            Transformed DataFrame with renamed columns
        """
        df = df.copy()

        # Get field mappings from YAML configuration
        rename_map = self.get_field_mappings()

        if my_logger:
            my_logger.debug(f"Field mappings for {self.data_type}: {rename_map}")

        # Only apply mappings for columns that exist in the DataFrame
        existing_rename_map = {k: v for k, v in rename_map.items() if k in df.columns}

        if existing_rename_map:
            df.rename(columns=existing_rename_map, inplace=True)
            if my_logger:
                my_logger.debug(f"Renamed {len(existing_rename_map)} columns using field mapping: {existing_rename_map}")

        return df


class ChangelogDataProcessor:
    """Specialized processor for changelog data."""
    
    def __init__(self):
        self.field_mapper = SpecializedFieldMapper('changelog')
    
    def process_dataframe(self, df: pd.DataFrame, my_logger=None) -> pd.DataFrame:
        """Process changelog DataFrame with all required transformations."""
        if df.shape[0] == 0:
            return df
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        _ = quick_save_dataframe_minimal(df, f"df_changelog_initial_{timestamp}.xlsx", path=f"c:/vishal/log/changelog")

        # Step 1: Normalize changelog data

        df = df.join(pd.json_normalize(df.changelog)).drop(columns=["changelog"])
        _ = quick_save_dataframe_minimal(df, f"df_changelog_normalized_{timestamp}.xlsx", path=f"c:/vishal/log/changelog")
        df.rename(columns={'id': 'issue_id', 'key': 'issue_key'}, inplace=True)


        # Step 3: Explode histories
        if 'histories' in df.columns:
            df = df.explode(column="histories")
            df['histories'] = df['histories'].apply(
                lambda x: {**x, 'items': json.dumps(x['items'])} if isinstance(x, dict) and 'items' in x else x
            )
            df = df.join(
                pd.json_normalize(df['histories'])
                [
                    [
                        "id", "created", "author.accountId", "items"
                    ]
                ]
            ).drop(columns=["histories", "startAt", "maxResults", "total"])

            # Step 5: Drop processing columns and normalize histories
            columns_to_drop = self.field_mapper.get_columns_to_drop()
            existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]
            if existing_columns_to_drop:
                df.drop(columns=existing_columns_to_drop, inplace=True)

            df = self.field_mapper.apply_field_mappings(df, my_logger)
        return df


class WorklogDataProcessor:
    """Specialized processor for worklog data."""
    
    def __init__(self):
        self.field_mapper = SpecializedFieldMapper('worklog')

    def process_dataframe(self, df: pd.DataFrame, my_logger=None) -> tuple[pd.DataFrame, list]:
        """Process worklog DataFrame with all required transformations."""
        if df.shape[0] == 0:
            return df, []

        # Step 1: Normalize worklog data
        if 'worklog' in df.columns:
            df = df.join(pd.json_normalize(df.worklog)).drop(columns=["worklog"])

        # there is an instance in CPP-470 where worklogs is empty but total is 1
        # Therefore, such rows need to be filtered out
        df.query("total > 0 and worklogs.str.len() > 0", inplace=True)

        if df.shape[0] == 0:
            return df, []

        worklog_keys: list = df.query("total > maxResults")['key'].tolist()
        df.drop(columns=["startAt", "maxResults", "total"], inplace=True)
        df.rename(columns={'key': 'issue_key'}, inplace=True)
        # Rename columns handled by field mappings later

        df = df.explode("worklogs").reset_index(drop=True)

        # df['worklogs'] = df['worklogs'].apply(
        #     lambda x: {**x, 'comment': json.dumps(x['comment'])} if isinstance(x, dict) and 'comment' in x else x
        # )

        df['worklogs'] = df['worklogs'].apply(
            lambda x: {
                **x,
                'comment': json.dumps(x['comment']) if 'comment' in x else None,
                'author': {'accountId': x['author']['accountId']} if isinstance(x['author'], dict) else x['author'],
                'updateAuthor': {'accountId': x['updateAuthor']['accountId']} if 'updateAuthor' in x and isinstance(
                    x['updateAuthor'], dict) else x.get('updateAuthor')
            } if isinstance(x, dict) else x
        )

        df = df.join(
            pd.json_normalize(df['worklogs'])
        ).drop(columns=["worklogs"])
        return df, worklog_keys


class CommentDataProcessor:
    """Specialized processor for comment data."""
    
    def __init__(self):
        self.field_mapper = SpecializedFieldMapper('comment')
    
    def process_dataframe(self, df: pd.DataFrame, my_logger=None) -> pd.DataFrame:
        """Process comment DataFrame with all required transformations."""
        if df.shape[0] == 0:
            return df

        # if df.shape[0] == 0:
        #     return df
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Step 2: Rename initial columns (handled by field mappings later)
        _ = quick_save_dataframe_minimal(df, f"df_comments_rename_{timestamp}.xlsx", path=f"c:/vishal/log/issue_comment")
        
        # Step 3: Explode comments
        df = df.explode(column="comments").reset_index(drop=True)
        df["comments"] = df["comments"].apply(
            lambda x: {
                **x,
                'body': json.dumps(x['body']) if 'body' in x else None,
                'author': {'accountId': x['author']['accountId']} if isinstance(x['author'], dict) else x['author'],
                'updateAuthor': {'accountId': x['updateAuthor']['accountId']} if 'updateAuthor' in x and isinstance(
                    x['updateAuthor'], dict) else x['updateAuthor']
            } if isinstance(x, dict)  else x,

        )
        # df = df.join(
        #     pd.json_normalize(df['comments'])
        # ).drop(columns=["comments"])


        # try:
        #     df = df.join(
        #         pd.json_normalize(df["comments"])
        #         [
        #             [
        #                 "id", "author.accountId", "updateAuthor.accountId",
        #                 "created", "updated", "jsdPublic", "body.version", "body.type", "body.content"
        #             ]
        #         ]
        #     ).drop(columns=["comments", "maxResults", "total", "startAt"])
        # except KeyError as e:
        #     exc_type, exc_value, exc_tb = sys.exc_info()
        #     tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        #     my_logger.error(''.join(tb.format_exception_only()))
        #     my_logger.error(e)
        #
        # _ = quick_save_dataframe(df, f"df_comments_explode_join_{timestamp}.xlsx", path=f"c:/vishal/log/issue_comment")
        #
        # df["body"] = df.apply(
        #     lambda x: {
        #         "version": x.get("body.version", 1),
        #         "type": x.get("body.type", "doc"),
        #         "content": x.get("body.content", [])
        #     }, axis=1
        # )
        # df.drop(columns=["body.version", "body.type", "body.content"], inplace=True)

        # Step 5: Drop unwanted columns
        # df = self.field_mapper.drop_unwanted_columns(df, my_logger)

        # Step 6: Apply field mappings from YAML configuration
        # df = self.field_mapper.apply_field_mappings(df, my_logger)

        return df


class IssueLinksDataProcessor:
    """Specialized processor for issue links data."""
    
    def __init__(self):
        self.field_mapper = SpecializedFieldMapper('issue_links')
    
    def process_dataframe(self, df: pd.DataFrame, my_logger=None) -> pd.DataFrame:
        """Process issue links DataFrame with all required transformations."""
        if df.shape[0] == 0:
            return df
        
        # Step 1: Explode issuelinks
        if 'issuelinks' in df.columns:
            df = df.explode(column="issuelinks")
            # Rename columns handled by field mappings later
            # df.dropna(subset=["issuelinks"], inplace=True)

            df = pd.concat(
                [
                    df.drop(columns="issuelinks").reset_index(drop=True),
                    pd.json_normalize(
                        df['issuelinks'],
                        max_level=0
                    ).drop(columns="self")
                ], axis=1
            )

            # Step 2: Add issue info to issuelinks

            # df.issuelinks = df.apply(
            #     lambda x: add_issue_key_issue_id(x['issuelinks'], x['key'], x['id'], my_logger),
            #     axis=1
            # )
            #
            # Step 3: Normalize issuelinks JSON
            # df = pd.json_normalize(df.issuelinks)
            
            # Step 4: Flatten nested structures
            # Handle type.* fields
            # type_columns = [col for col in df.columns if col.startswith('type.')]
            # for col in type_columns:
            #     new_name = col.replace('type.', 'type_')
            #     df.rename(columns={col: new_name}, inplace=True)
            
            # Handle inwardIssue.* and outwardIssue.* fields
            for prefix in ['inwardIssue', 'outwardIssue']:
                prefix_columns = [col for col in df.columns if col.startswith(f'{prefix}.')]
                for col in prefix_columns:
                    new_name = col.replace(f'{prefix}.', f'{prefix}_')
                    df.rename(columns={col: new_name}, inplace=True)

            # Drop unwanted columns
            df = self.field_mapper.drop_unwanted_columns(df, my_logger)

            # Apply field mappings from YAML configuration
            df = self.field_mapper.apply_field_mappings(df, my_logger)

        return df


# Factory functions
def create_specialized_processor(data_type: str):
    """Create a specialized processor for the given data type."""
    processors = {
        'changelog': ChangelogDataProcessor,
        'worklog': WorklogDataProcessor,
        'comment': CommentDataProcessor,
        'issue_links': IssueLinksDataProcessor
    }
    
    processor_class = processors.get(data_type)
    if not processor_class:
        raise ValueError(f"Unknown data type: {data_type}")
    
    return processor_class()
